version: '3.8'
services:
  products:
    build:
      context: ../src/Products/src
      dockerfile: Dockerfile
    container_name: microcommerce-products
    environment:
      - ASPNETCORE_ENVIRONMENT=Docker
    ports:
      - "5001:8080"

  categories:
    build:
      context: ../src/Categories/src
      dockerfile: Dockerfile
    container_name: microcommerce-categories
    environment:
      - ASPNETCORE_ENVIRONMENT=Docker
    ports:
      - "5002:8080"

networks:
  default:
    name: microcommerce-net
    external: true